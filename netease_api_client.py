#!/usr/bin/env python3
"""
网易云音乐API客户端
基于开源项目: https://github.com/Suxiaoqinx/Netease_url
"""

import requests
import json
from typing import Dict, Union, Optional
from urllib.parse import urlparse, parse_qs

class NeteaseAPIClient:
    """网易云音乐API客户端"""
    
    def __init__(self, base_url: str = "https://api.toubiec.cn"):
        self.base_url = base_url
        self.endpoints = {
            'song': '/Song_V1',
            'search': '/Search', 
            'playlist': '/Playlist',
            'album': '/Album'
        }
        
        self.quality_levels = {
            'standard': '标准音质',
            'exhigh': '极高音质',
            'lossless': '无损音质(VIP)',
            'hires': 'Hi-Res音质(VIP)',
            'jyeffect': '高清环绕声(VIP)',
            'sky': '沉浸环绕声(SVIP)',
            'jymaster': '超清母带(SVIP)'
        }
    
    def extract_song_id(self, url_or_id: str) -> str:
        """从URL中提取歌曲ID"""
        if url_or_id.isdigit():
            return url_or_id
            
        try:
            parsed = urlparse(url_or_id)
            if 'id=' in parsed.query:
                return parse_qs(parsed.query)['id'][0]
            elif parsed.path.startswith('/song/'):
                return parsed.path.split('/')[-1]
        except:
            pass
            
        return url_or_id
    
    def parse_song(self, song_url: str, quality: str = 'lossless', 
                   return_type: str = 'json') -> Dict:
        """
        解析单曲
        
        Args:
            song_url: 网易云音乐链接或歌曲ID
            quality: 音质等级 (standard/exhigh/lossless/hires/jyeffect/sky/jymaster)
            return_type: 返回类型 (json/text/down)
            
        Returns:
            解析结果字典
        """
        url = self.base_url + self.endpoints['song']
        
        data = {
            'url': song_url,
            'level': quality,
            'type': return_type
        }
        
        try:
            response = requests.post(url, data=data, timeout=30)
            
            if return_type == 'json':
                try:
                    return response.json()
                except json.JSONDecodeError:
                    return {
                        'error': '响应不是有效的JSON',
                        'status_code': response.status_code,
                        'raw_response': response.text
                    }
            else:
                return {
                    'status_code': response.status_code,
                    'content': response.text
                }
                
        except requests.RequestException as e:
            return {'error': f'请求失败: {str(e)}'}
    
    def search_music(self, keywords: str, limit: int = 10) -> Dict:
        """
        搜索音乐
        
        Args:
            keywords: 搜索关键词
            limit: 返回数量
            
        Returns:
            搜索结果
        """
        url = self.base_url + self.endpoints['search']
        
        data = {
            'keywords': keywords,
            'limit': limit
        }
        
        try:
            response = requests.post(url, data=data, timeout=30)
            return response.json()
        except Exception as e:
            return {'error': f'搜索失败: {str(e)}'}
    
    def get_playlist(self, playlist_id: str) -> Dict:
        """
        获取歌单信息
        
        Args:
            playlist_id: 歌单ID
            
        Returns:
            歌单信息
        """
        url = self.base_url + self.endpoints['playlist']
        
        data = {'id': playlist_id}
        
        try:
            response = requests.post(url, data=data, timeout=30)
            return response.json()
        except Exception as e:
            return {'error': f'获取歌单失败: {str(e)}'}
    
    def get_album(self, album_id: str) -> Dict:
        """
        获取专辑信息
        
        Args:
            album_id: 专辑ID
            
        Returns:
            专辑信息
        """
        url = self.base_url + self.endpoints['album']
        
        data = {'id': album_id}
        
        try:
            response = requests.post(url, data=data, timeout=30)
            return response.json()
        except Exception as e:
            return {'error': f'获取专辑失败: {str(e)}'}

def test_api():
    """测试API功能"""
    print("🚀 开始测试网易云音乐API...\n")
    
    client = NeteaseAPIClient()
    
    # 测试用例
    test_cases = [
        {
            'name': '测试1: 无损音质解析',
            'song_url': 'https://music.163.com/song?id=1901371647',
            'quality': 'lossless',
            'type': 'json'
        },
        {
            'name': '测试2: 标准音质解析',
            'song_url': '1901371647',  # 直接使用ID
            'quality': 'standard', 
            'type': 'json'
        },
        {
            'name': '测试3: 文本格式返回',
            'song_url': 'https://music.163.com/song?id=1901371647',
            'quality': 'lossless',
            'type': 'text'
        }
    ]
    
    for test_case in test_cases:
        print(f"📊 {test_case['name']}")
        print(f"🎵 歌曲: {test_case['song_url']}")
        print(f"🎼 音质: {client.quality_levels.get(test_case['quality'], test_case['quality'])}")
        print(f"📋 类型: {test_case['type']}")
        
        result = client.parse_song(
            test_case['song_url'], 
            test_case['quality'], 
            test_case['type']
        )
        
        if 'error' in result:
            print(f"❌ 错误: {result['error']}")
            if 'raw_response' in result:
                print(f"📄 原始响应: {result['raw_response']}")
        elif test_case['type'] == 'json' and result.get('status') == 200:
            print("✅ 解析成功!")
            print(f"🎵 歌曲名: {result.get('name', 'N/A')}")
            print(f"🎤 歌手: {result.get('ar_name', 'N/A')}")
            print(f"💿 专辑: {result.get('al_name', 'N/A')}")
            print(f"🎼 音质: {result.get('level', 'N/A')}")
            print(f"📦 大小: {result.get('size', 'N/A')}")
            print(f"🔗 链接: {'✅ 获取成功' if result.get('url') else '❌ 获取失败'}")
        else:
            print(f"📄 响应: {result}")
        
        print("─" * 50)

def show_usage():
    """显示使用说明"""
    print("""
🎵 网易云音乐API使用说明

📋 支持的音质等级:
  standard: 标准音质
  exhigh: 极高音质
  lossless: 无损音质(VIP)
  hires: Hi-Res音质(VIP)
  jyeffect: 高清环绕声(VIP)
  sky: 沉浸环绕声(SVIP)
  jymaster: 超清母带(SVIP)

📋 支持的返回类型:
  json: JSON格式详细信息
  text: 纯文本格式
  down: 直接下载链接

🔧 使用示例:
  client = NeteaseAPIClient()
  result = client.parse_song('https://music.163.com/song?id=1901371647', 'lossless', 'json')

⚠️  注意事项:
  - VIP/SVIP音质需要对应权限的Cookie
  - 请遵守版权法律法规
  - 仅供学习研究使用
""")

if __name__ == "__main__":
    print("🎵 网易云音乐API测试工具")
    print("基于开源项目: https://github.com/Suxiaoqinx/Netease_url\n")
    
    show_usage()
    test_api()

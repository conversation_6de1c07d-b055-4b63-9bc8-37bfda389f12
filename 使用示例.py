#!/usr/bin/env python3
"""
网易云音乐API使用示例
演示如何使用API进行音乐解析和下载
"""

from netease_api_client import NeteaseAPIClient
import requests
import os
from urllib.parse import urlparse

def download_music(url: str, filename: str = None) -> bool:
    """
    下载音乐文件
    
    Args:
        url: 音乐文件URL
        filename: 保存的文件名
        
    Returns:
        是否下载成功
    """
    try:
        if not filename:
            # 从URL中提取文件名
            parsed = urlparse(url)
            filename = os.path.basename(parsed.path) or "music.flac"
        
        print(f"📥 开始下载: {filename}")
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        file_size = os.path.getsize(filename)
        print(f"✅ 下载完成: {filename} ({file_size / 1024 / 1024:.2f} MB)")
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {str(e)}")
        return False

def parse_and_download_example():
    """解析并下载音乐示例"""
    print("🎵 网易云音乐解析和下载示例\n")
    
    # 创建API客户端
    client = NeteaseAPIClient()
    
    # 示例歌曲链接
    song_url = "https://music.163.com/song?id=1901371647"  # 孤勇者
    
    print(f"🔍 正在解析: {song_url}")
    
    # 解析歌曲信息
    result = client.parse_song(song_url, quality='lossless', return_type='json')
    
    if 'error' in result:
        print(f"❌ 解析失败: {result['error']}")
        return
    
    if result.get('status') != 200:
        print(f"❌ 解析失败: {result.get('msg', '未知错误')}")
        return
    
    # 显示歌曲信息
    print("✅ 解析成功!")
    print(f"🎵 歌曲名: {result.get('name')}")
    print(f"🎤 歌手: {result.get('ar_name')}")
    print(f"💿 专辑: {result.get('al_name')}")
    print(f"🎼 音质: {result.get('level')}")
    print(f"📦 大小: {result.get('size')}")
    
    # 获取下载链接
    download_url = result.get('url')
    if not download_url:
        print("❌ 未获取到下载链接")
        return
    
    print(f"🔗 下载链接: {download_url}")
    
    # 构建文件名
    filename = f"{result.get('ar_name', 'Unknown')} - {result.get('name', 'Unknown')}.flac"
    # 清理文件名中的非法字符
    filename = "".join(c for c in filename if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
    
    # 询问是否下载
    choice = input(f"\n📥 是否下载到本地? (y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        download_music(download_url, filename)
    else:
        print("⏭️  跳过下载")

def search_example():
    """搜索音乐示例"""
    print("\n🔍 音乐搜索示例")
    
    client = NeteaseAPIClient()
    
    # 搜索关键词
    keywords = "孤勇者"
    print(f"🔍 搜索关键词: {keywords}")
    
    result = client.search_music(keywords, limit=5)
    
    if 'error' in result:
        print(f"❌ 搜索失败: {result['error']}")
        return
    
    if result.get('status') == 200:
        songs = result.get('result', [])
        print(f"✅ 找到 {len(songs)} 首歌曲:")
        
        for i, song in enumerate(songs, 1):
            print(f"{i}. {song.get('name')} - {song.get('artists')}")
            print(f"   专辑: {song.get('album')}")
            print(f"   ID: {song.get('id')}")
            print()
    else:
        print(f"❌ 搜索失败: {result}")

def batch_download_example():
    """批量下载示例"""
    print("\n📦 批量下载示例")
    
    client = NeteaseAPIClient()
    
    # 示例歌曲ID列表
    song_ids = [
        "1901371647",  # 孤勇者
        "1868553",     # 稻香
        "287016"       # 青花瓷
    ]
    
    print(f"📋 准备下载 {len(song_ids)} 首歌曲")
    
    for i, song_id in enumerate(song_ids, 1):
        print(f"\n📊 处理第 {i}/{len(song_ids)} 首")
        
        result = client.parse_song(song_id, quality='standard', return_type='json')
        
        if 'error' in result or result.get('status') != 200:
            print(f"❌ 解析失败: {song_id}")
            continue
        
        print(f"🎵 {result.get('name')} - {result.get('ar_name')}")
        
        # 这里可以添加实际的下载逻辑
        # download_music(result.get('url'), f"{result.get('name')}.mp3")

def main():
    """主函数"""
    print("🎵 网易云音乐API使用示例")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择功能:")
        print("1. 解析并下载单曲")
        print("2. 搜索音乐")
        print("3. 批量下载示例")
        print("0. 退出")
        
        choice = input("\n请输入选项 (0-3): ").strip()
        
        if choice == '1':
            parse_and_download_example()
        elif choice == '2':
            search_example()
        elif choice == '3':
            batch_download_example()
        elif choice == '0':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选项，请重新选择")

if __name__ == "__main__":
    main()

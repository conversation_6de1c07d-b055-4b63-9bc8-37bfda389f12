# 网易云音乐API接口抓取完整指南

## 📋 项目概述

基于开源项目 [Suxiaoqinx/Netease_url](https://github.com/Suxiaoqinx/Netease_url) 的深度分析，本文档提供完整的网易云音乐解析API使用方法和SVIP令牌获取方案。

## 🔗 核心API接口

### 主要解析接口
```
POST https://api.toubiec.cn/api/music_v1.php
```

### 辅助接口
```
POST https://api.toubiec.cn/api/get-token.php  # 获取访问令牌
```

### 必需参数
| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `url` 或 `ids` | string | 网易云音乐链接或歌曲ID（二选一） | `https://music.163.com/song?id=1901371647` 或 `1901371647` |
| `level` | string | 音质等级 | `lossless` |
| `type` | string | 返回类型 | `json` / `text` / `down` |

### 音质等级说明
| 参数值 | 音质名称 | 权限要求 | 文件格式 |
|--------|----------|----------|----------|
| `standard` | 标准音质 | 免费 | MP3 128kbps |
| `exhigh` | 极高音质 | 免费 | MP3 320kbps |
| `lossless` | 无损音质 | VIP | FLAC |
| `hires` | Hi-Res音质 | VIP | FLAC 24bit |
| `jyeffect` | 高清环绕声 | VIP | FLAC 环绕声 |
| `sky` | 沉浸环绕声 | **SVIP** | FLAC 沉浸式 |
| `jymaster` | 超清母带 | **SVIP** | FLAC 母带级 |

### 返回类型说明
- `json`: 返回JSON格式的详细信息
- `text`: 返回纯文本格式的歌曲信息
- `down`: 直接重定向到音乐文件下载链接

## 🛠️ API调用示例

### 1. 使用curl命令
```bash
curl -X POST "https://api.toubiec.cn/api/music_v1.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "url=https://music.163.com/song?id=1901371647&level=lossless&type=json"
```

### 2. 使用Python requests
```python
import requests

def parse_netease_music(song_url, quality='lossless', return_type='json'):
    """
    解析网易云音乐
    
    Args:
        song_url: 网易云音乐链接或歌曲ID
        quality: 音质等级 (standard/exhigh/lossless/hires/jyeffect/sky/jymaster)
        return_type: 返回类型 (json/text/down)
    
    Returns:
        解析结果
    """
    api_url = "https://api.toubiec.cn/api/music_v1.php"
    
    data = {
        'url': song_url,
        'level': quality,
        'type': return_type
    }
    
    try:
        response = requests.post(api_url, data=data)
        
        if return_type == 'json':
            return response.json()
        else:
            return response.text
            
    except Exception as e:
        return {'error': str(e)}

# 使用示例
result = parse_netease_music(
    song_url='https://music.163.com/song?id=1901371647',
    quality='lossless',
    return_type='json'
)

print(result)
```

### 3. 使用JavaScript fetch
```javascript
async function parseNeteaseMusic(songUrl, quality = 'lossless', returnType = 'json') {
    const formData = new URLSearchParams();
    formData.append('url', songUrl);
    formData.append('level', quality);
    formData.append('type', returnType);
    
    try {
        const response = await fetch('https://api.toubiec.cn/api/music_v1.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: formData
        });
        
        if (returnType === 'json') {
            return await response.json();
        } else {
            return await response.text();
        }
    } catch (error) {
        return { error: error.message };
    }
}

// 使用示例
parseNeteaseMusic('https://music.163.com/song?id=1901371647', 'lossless', 'json')
    .then(result => console.log(result));
```

## 🔑 SVIP令牌获取方法

### 方法一：二维码登录（推荐）
使用开源项目提供的自动化登录功能：

```python
# 下载项目源码
git clone https://github.com/Suxiaoqinx/Netease_url.git
cd Netease_url

# 安装依赖
pip install -r requirements.txt

# 运行二维码登录
python qr_login.py
```

### 方法二：手动获取Cookie
1. 打开网易云音乐网页版并登录VIP/SVIP账号
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面或播放音乐
5. 找到任意请求，查看Request Headers中的Cookie
6. 复制`MUSIC_U`的值

### Cookie格式
```
MUSIC_U=你的MUSIC_U值;os=pc;appver=8.9.70;
```

## 🏗️ 自建解析服务

### 1. 部署开源项目
```bash
# 克隆项目
git clone https://github.com/Suxiaoqinx/Netease_url.git
cd Netease_url

# 安装依赖
pip install -r requirements.txt

# 配置Cookie
echo "MUSIC_U=你的MUSIC_U值;os=pc;appver=8.9.70;" > cookie.txt

# 启动API服务
python main.py --mode api
```

### 2. Docker部署
```bash
# 修改.env文件中的端口配置
# 启动服务
docker-compose up -d
```

## 📊 JSON响应格式

成功响应示例：
```json
{
    "status": 200,
    "name": "孤勇者",
    "pic": "https://p3.music.126.net/aG5zqxkBRfLiV7A8W0iwgA==/109951166702962263.jpg",
    "ar_name": "陈奕迅",
    "al_name": "孤勇者",
    "level": "无损音质 (SQ)",
    "size": "27.70MB",
    "url": "https://m801.music.126.net/...",
    "lyric": "[00:00.000] 作词 : 唐恬...",
    "tlyric": null
}
```

错误响应示例：
```json
{
    "status": 400,
    "msg": "type参数错误！"
}
```

## ⚠️ 注意事项

1. **权限要求**: 高音质需要对应的VIP/SVIP权限
2. **Cookie有效期**: 需要定期更新Cookie保持服务可用
3. **请求频率**: 避免过于频繁的请求，防止被限制
4. **版权声明**: 仅供学习研究使用，请尊重版权
5. **API限制**: 第三方API可能需要特殊的访问令牌或有其他限制
6. **服务稳定性**: 第三方服务可能随时停止或更改接口

## 🔧 故障排除

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| `type参数错误！` | type参数不正确 | 使用 `json`/`text`/`down` |
| `level参数为空` | 未提供音质参数 | 添加 `level` 参数 |
| `信息获取不完整！` | Cookie无效或权限不足 | 更新Cookie或检查VIP状态 |
| `Failed to fetch` | CORS或网络问题 | 使用服务端请求或代理 |

## 📈 扩展功能

### 其他可用接口
- 搜索: `POST /Search`
- 歌单: `POST /Playlist`  
- 专辑: `POST /Album`

详细参数请参考源码中的实现。

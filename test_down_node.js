// Node.js测试 type=down 参数

async function testDownType() {
    console.log('🔍 测试 type=down 参数...');
    
    const testCases = [
        {
            name: '测试1: 标准音质 + down类型',
            data: {
                url: 'https://music.163.com/song?id=1901371647',
                level: 'standard',
                type: 'down'
            }
        },
        {
            name: '测试2: 无损音质 + down类型',
            data: {
                url: 'https://music.163.com/song?id=1901371647',
                level: 'lossless',
                type: 'down'
            }
        },
        {
            name: '测试3: 歌曲ID + down类型',
            data: {
                url: '1901371647',
                level: 'standard',
                type: 'down'
            }
        }
    ];
    
    for (const testCase of testCases) {
        console.log(`\n📊 ${testCase.name}`);
        console.log(`📊 请求数据:`, testCase.data);
        
        try {
            const formData = new URLSearchParams();
            Object.keys(testCase.data).forEach(key => {
                formData.append(key, testCase.data[key]);
            });
            
            const response = await fetch('https://api.toubiec.cn/api/music_v1.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData,
                redirect: 'manual'  // 不自动跟随重定向
            });
            
            console.log(`📈 状态码: ${response.status}`);
            console.log(`📄 响应头:`, Object.fromEntries(response.headers.entries()));
            
            if (response.status >= 300 && response.status < 400) {
                // 重定向响应
                const location = response.headers.get('location');
                if (location) {
                    console.log(`🔗 重定向到: ${location}`);
                    console.log('✅ 成功获取直接下载链接!');
                    
                    // 验证链接
                    try {
                        const headResponse = await fetch(location, { method: 'HEAD' });
                        console.log(`🎵 文件验证状态: ${headResponse.status}`);
                        const contentType = headResponse.headers.get('content-type');
                        const contentLength = headResponse.headers.get('content-length');
                        console.log(`🎵 文件类型: ${contentType}`);
                        if (contentLength) {
                            console.log(`🎵 文件大小: ${(parseInt(contentLength) / 1024 / 1024).toFixed(2)} MB`);
                        }
                    } catch (e) {
                        console.log(`⚠️ 链接验证失败: ${e.message}`);
                    }
                } else {
                    console.log('❌ 重定向但无Location头');
                }
            } else {
                // 非重定向响应
                const text = await response.text();
                console.log(`📄 响应内容长度: ${text.length}`);
                console.log(`📄 响应内容: ${text.substring(0, 200)}...`);
                
                // 尝试解析JSON
                try {
                    const json = JSON.parse(text);
                    console.log(`📊 JSON数据:`, json);
                    if (json.url) {
                        console.log('✅ JSON中包含下载链接!');
                        console.log(`🔗 下载链接: ${json.url}`);
                    }
                } catch (e) {
                    console.log('❌ 非JSON响应');
                }
            }
            
        } catch (error) {
            console.error(`❌ 请求失败: ${error.message}`);
        }
        
        console.log('─'.repeat(60));
    }
}

// 运行测试
testDownType().catch(console.error);

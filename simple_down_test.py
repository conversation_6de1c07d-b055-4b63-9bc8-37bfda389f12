#!/usr/bin/env python3
"""
简单测试 type=down 参数
"""

import requests

def test_down():
    url = "https://api.toubiec.cn/api/music_v1.php"
    
    data = {
        'url': 'https://music.163.com/song?id=1901371647',
        'level': 'standard',
        'type': 'down'
    }
    
    print("🔍 测试 type=down...")
    print(f"📊 请求: {url}")
    print(f"📊 数据: {data}")
    
    try:
        # 不跟随重定向
        response = requests.post(url, data=data, allow_redirects=False, timeout=20)
        
        print(f"📈 状态码: {response.status_code}")
        
        if response.status_code in [301, 302, 303, 307, 308]:
            location = response.headers.get('Location')
            print(f"🔗 重定向到: {location}")
            if location:
                print("✅ 成功获取直接下载链接!")
                return location
        else:
            print(f"📄 响应内容: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    return None

if __name__ == "__main__":
    result = test_down()
    if result:
        print(f"\n🎵 下载链接: {result}")
    else:
        print("\n❌ 未获取到下载链接")

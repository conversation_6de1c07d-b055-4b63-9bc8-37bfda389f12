// 详细的API测试 - 模拟完整的浏览器请求

async function testWithBrowserHeaders() {
    console.log('🔍 使用完整浏览器头测试API...');
    
    const testData = {
        url: 'https://music.163.com/song?id=1901371647',
        level: 'standard',
        type: 'json'  // 先测试json类型
    };
    
    const formData = new URLSearchParams();
    Object.keys(testData).forEach(key => {
        formData.append(key, testData[key]);
    });
    
    // 模拟完整的浏览器请求头
    const headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://api.toubiec.cn',
        'Referer': 'https://api.toubiec.cn/wyapi.html',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    };
    
    console.log('📊 请求数据:', testData);
    console.log('📊 请求头:', headers);
    
    try {
        const response = await fetch('https://api.toubiec.cn/api/music_v1.php', {
            method: 'POST',
            headers: headers,
            body: formData,
            credentials: 'include'  // 包含cookies
        });
        
        console.log(`📈 状态码: ${response.status}`);
        console.log(`📄 响应头:`, Object.fromEntries(response.headers.entries()));
        
        const text = await response.text();
        console.log(`📄 响应内容长度: ${text.length}`);
        console.log(`📄 响应内容: ${text}`);
        
        if (text.length > 0) {
            try {
                const json = JSON.parse(text);
                console.log('✅ JSON解析成功:', json);
                return json;
            } catch (e) {
                console.log('❌ JSON解析失败，原始内容:', text);
            }
        } else {
            console.log('❌ 响应为空');
        }
        
    } catch (error) {
        console.error('❌ 请求失败:', error);
    }
    
    return null;
}

async function testGetToken() {
    console.log('\n🔑 尝试获取访问令牌...');
    
    try {
        // 先访问主页面，可能需要获取session
        const homeResponse = await fetch('https://api.toubiec.cn/wyapi.html', {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        });
        
        console.log(`🏠 主页状态: ${homeResponse.status}`);
        
        // 检查是否有set-cookie
        const cookies = homeResponse.headers.get('set-cookie');
        if (cookies) {
            console.log(`🍪 获取到cookies: ${cookies}`);
        }
        
        // 尝试获取token的API
        const tokenResponse = await fetch('https://api.toubiec.cn/api/get-token.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': 'https://api.toubiec.cn/wyapi.html'
            }
        });
        
        console.log(`🔑 Token API状态: ${tokenResponse.status}`);
        const tokenText = await tokenResponse.text();
        console.log(`🔑 Token响应: ${tokenText}`);
        
    } catch (error) {
        console.error('❌ 获取token失败:', error);
    }
}

async function testDifferentEndpoints() {
    console.log('\n🔍 测试不同的API端点...');
    
    const endpoints = [
        '/api/music_v1.php',
        '/Song_V1',
        '/Song_v1',
        '/api/song',
        '/music'
    ];
    
    const testData = {
        url: 'https://music.163.com/song?id=1901371647',
        level: 'standard',
        type: 'json'
    };
    
    for (const endpoint of endpoints) {
        console.log(`\n📊 测试端点: ${endpoint}`);
        
        try {
            const formData = new URLSearchParams();
            Object.keys(testData).forEach(key => {
                formData.append(key, testData[key]);
            });
            
            const response = await fetch(`https://api.toubiec.cn${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Referer': 'https://api.toubiec.cn/wyapi.html'
                },
                body: formData
            });
            
            console.log(`   状态码: ${response.status}`);
            const text = await response.text();
            console.log(`   响应长度: ${text.length}`);
            console.log(`   响应内容: ${text.substring(0, 100)}...`);
            
        } catch (error) {
            console.log(`   错误: ${error.message}`);
        }
    }
}

async function runAllTests() {
    console.log('🎵 开始详细API测试\n');
    
    // 1. 测试获取token
    await testGetToken();
    
    // 2. 测试不同端点
    await testDifferentEndpoints();
    
    // 3. 测试完整浏览器头
    await testWithBrowserHeaders();
    
    console.log('\n✅ 测试完成');
}

// 运行测试
runAllTests().catch(console.error);

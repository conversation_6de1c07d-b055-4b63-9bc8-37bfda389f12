#!/usr/bin/env python3
"""
测试 type=down 参数，获取直接下载链接
"""

import requests
import json

def test_down_type():
    """测试down类型返回"""
    print("🔍 测试 type=down 参数...")
    
    url = "https://api.toubiec.cn/api/music_v1.php"
    
    # 测试用例
    test_cases = [
        {
            'name': '测试1: 标准音质 + down类型',
            'data': {
                'url': 'https://music.163.com/song?id=1901371647',
                'level': 'standard',
                'type': 'down'
            }
        },
        {
            'name': '测试2: 无损音质 + down类型',
            'data': {
                'url': 'https://music.163.com/song?id=1901371647',
                'level': 'lossless',
                'type': 'down'
            }
        },
        {
            'name': '测试3: 使用歌曲ID + down类型',
            'data': {
                'url': '1901371647',
                'level': 'standard',
                'type': 'down'
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📊 {test_case['name']}")
        print(f"📊 请求数据: {test_case['data']}")
        
        try:
            # 发送请求，不自动跟随重定向
            response = requests.post(url, data=test_case['data'], 
                                   allow_redirects=False, timeout=30)
            
            print(f"📈 状态码: {response.status_code}")
            print(f"📄 响应头: {dict(response.headers)}")
            
            # 检查是否有重定向
            if response.status_code in [301, 302, 303, 307, 308]:
                location = response.headers.get('Location')
                if location:
                    print(f"🔗 重定向到: {location}")
                    print("✅ 成功获取直接下载链接!")
                    
                    # 验证链接是否有效
                    try:
                        head_response = requests.head(location, timeout=10)
                        print(f"🎵 文件验证状态: {head_response.status_code}")
                        content_type = head_response.headers.get('content-type', '')
                        content_length = head_response.headers.get('content-length', '0')
                        print(f"🎵 文件类型: {content_type}")
                        print(f"🎵 文件大小: {int(content_length) / 1024 / 1024:.2f} MB" if content_length.isdigit() else "未知")
                    except Exception as e:
                        print(f"⚠️ 链接验证失败: {e}")
                else:
                    print("❌ 重定向但无Location头")
            else:
                # 非重定向响应
                content = response.text
                print(f"📄 响应内容长度: {len(content)}")
                print(f"📄 响应内容: {content[:200]}...")
                
                # 尝试解析JSON
                try:
                    json_data = json.loads(content)
                    print(f"📊 JSON数据: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
                except:
                    print("❌ 非JSON响应")
                    
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
        
        print("─" * 60)

def test_with_curl():
    """使用curl命令测试"""
    print("\n🔧 使用curl命令测试...")
    
    import subprocess
    
    curl_commands = [
        [
            'curl', '-X', 'POST',
            'https://api.toubiec.cn/api/music_v1.php',
            '-H', 'Content-Type: application/x-www-form-urlencoded',
            '-d', 'url=https://music.163.com/song?id=1901371647&level=standard&type=down',
            '-i', '--max-redirs', '0'
        ]
    ]
    
    for cmd in curl_commands:
        print(f"📊 执行命令: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            print(f"📈 返回码: {result.returncode}")
            print(f"📄 输出: {result.stdout}")
            if result.stderr:
                print(f"❌ 错误: {result.stderr}")
        except Exception as e:
            print(f"❌ 命令执行失败: {e}")

if __name__ == "__main__":
    print("🎵 测试网易云API的down类型返回")
    print("=" * 60)
    
    test_down_type()
    test_with_curl()

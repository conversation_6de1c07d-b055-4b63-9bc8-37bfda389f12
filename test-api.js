/**
 * 测试您的API是否正常工作
 */

const API_BASE = 'https://suwyy.deno.dev';

// 测试API状态
async function testAPIStatus() {
  console.log('🔍 测试API状态...');
  
  try {
    const response = await fetch(`${API_BASE}/api/status`);
    const data = await response.json();
    
    console.log('✅ API状态响应:', data);
    
    if (!data.cookieConfigured) {
      console.log('❌ Cookie未配置，这是落雪音乐无法工作的原因！');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ API状态检查失败:', error.message);
    return false;
  }
}

// 测试单曲解析
async function testSongAPI() {
  console.log('🎵 测试单曲解析API...');
  
  const testData = {
    ids: '1901371647', // 测试歌曲ID
    level: 'lossless'
  };
  
  try {
    const response = await fetch(`${API_BASE}/api/song`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    
    const data = await response.json();
    
    console.log('📊 API响应状态:', response.status);
    console.log('📊 API响应数据:', data);
    
    if (data.status === 200 && data.url) {
      console.log('✅ 单曲解析成功!');
      console.log(`🎵 歌曲: ${data.name}`);
      console.log(`🎤 歌手: ${data.ar_name}`);
      console.log(`🎼 音质: ${data.level}`);
      console.log(`🔗 链接: ${data.url}`);
      return true;
    } else {
      console.log('❌ 单曲解析失败:', data.error);
      return false;
    }
  } catch (error) {
    console.error('❌ 单曲API测试失败:', error.message);
    return false;
  }
}

// 测试CORS
async function testCORS() {
  console.log('🌐 测试CORS设置...');
  
  try {
    const response = await fetch(`${API_BASE}/api/status`, {
      method: 'OPTIONS'
    });
    
    console.log('📊 OPTIONS响应状态:', response.status);
    console.log('📊 CORS头部:', {
      'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
      'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
      'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
    });
    
    if (response.status === 200) {
      console.log('✅ CORS设置正常');
      return true;
    } else {
      console.log('❌ CORS设置可能有问题');
      return false;
    }
  } catch (error) {
    console.error('❌ CORS测试失败:', error.message);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试您的API...\n');
  
  const statusOK = await testAPIStatus();
  console.log('');
  
  const corsOK = await testCORS();
  console.log('');
  
  if (statusOK) {
    const songOK = await testSongAPI();
    console.log('');
    
    if (songOK) {
      console.log('🎉 所有测试通过！您的API可以正常使用');
      console.log('📝 落雪音乐自定义源应该能正常工作');
    } else {
      console.log('❌ 单曲解析测试失败，请检查API实现');
    }
  } else {
    console.log('❌ API状态检查失败，请先解决基础问题');
    console.log('💡 建议：');
    console.log('   1. 检查Deno Deploy环境变量NETEASE_COOKIE是否设置');
    console.log('   2. 确认Cookie格式正确');
    console.log('   3. 检查API服务是否正常运行');
  }
}

// 运行测试
runTests();

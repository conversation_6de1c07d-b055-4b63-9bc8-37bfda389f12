<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 type=down 参数</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-case { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e6ffe6; border: 1px solid #99ff99; }
        .error { background: #ffe6e6; border: 1px solid #ff9999; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🎵 测试网易云API的down类型返回</h1>
    
    <div class="test-case">
        <h3>测试说明</h3>
        <p>测试 <code>type=down</code> 参数是否能直接获取音乐文件下载链接</p>
        <button onclick="runAllTests()">开始测试</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <div id="results"></div>

    <script>
        async function testDownType(testName, songUrl, quality) {
            const resultsDiv = document.getElementById('results');
            const testDiv = document.createElement('div');
            testDiv.className = 'test-case';
            testDiv.innerHTML = `<h3>${testName}</h3><p>正在测试...</p>`;
            resultsDiv.appendChild(testDiv);

            try {
                const formData = new URLSearchParams();
                formData.append('url', songUrl);
                formData.append('level', quality);
                formData.append('type', 'down');

                console.log(`🔍 测试: ${testName}`);
                console.log(`📊 请求数据:`, Object.fromEntries(formData));

                // 使用fetch，不自动跟随重定向
                const response = await fetch('https://api.toubiec.cn/api/music_v1.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: formData,
                    redirect: 'manual'  // 不自动跟随重定向
                });

                console.log(`📈 状态码: ${response.status}`);
                console.log(`📄 响应头:`, Object.fromEntries(response.headers.entries()));

                let resultHTML = `
                    <h3>${testName}</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>请求参数:</strong></p>
                    <pre>${JSON.stringify(Object.fromEntries(formData), null, 2)}</pre>
                `;

                if (response.status === 0) {
                    // 重定向被拦截，这实际上可能意味着有重定向
                    resultHTML += `
                        <div class="result success">
                            <p>✅ 检测到重定向响应！</p>
                            <p>⚠️ 由于CORS限制，无法直接获取重定向URL</p>
                            <p>💡 这表明API确实支持type=down参数</p>
                        </div>
                    `;
                    testDiv.className = 'test-case success';
                } else if (response.status >= 300 && response.status < 400) {
                    // 重定向状态码
                    const location = response.headers.get('Location');
                    if (location) {
                        resultHTML += `
                            <div class="result success">
                                <p>✅ 成功获取重定向链接！</p>
                                <p><strong>下载链接:</strong></p>
                                <pre>${location}</pre>
                                <a href="${location}" target="_blank">🎵 点击下载</a>
                            </div>
                        `;
                        testDiv.className = 'test-case success';
                    } else {
                        resultHTML += `
                            <div class="result error">
                                <p>❌ 重定向但无Location头</p>
                            </div>
                        `;
                        testDiv.className = 'test-case error';
                    }
                } else {
                    // 其他状态码
                    const text = await response.text();
                    resultHTML += `
                        <div class="result">
                            <p><strong>响应内容:</strong></p>
                            <pre>${text.substring(0, 500)}${text.length > 500 ? '...' : ''}</pre>
                        </div>
                    `;
                    
                    // 尝试解析JSON
                    try {
                        const json = JSON.parse(text);
                        if (json.url) {
                            resultHTML += `
                                <div class="result success">
                                    <p>✅ JSON中包含下载链接！</p>
                                    <p><strong>下载链接:</strong></p>
                                    <pre>${json.url}</pre>
                                    <a href="${json.url}" target="_blank">🎵 点击下载</a>
                                </div>
                            `;
                            testDiv.className = 'test-case success';
                        }
                    } catch (e) {
                        // 非JSON响应
                    }
                }

                testDiv.innerHTML = resultHTML;

            } catch (error) {
                console.error(`❌ 测试失败: ${testName}`, error);
                testDiv.innerHTML = `
                    <h3>${testName}</h3>
                    <div class="result error">
                        <p>❌ 请求失败: ${error.message}</p>
                    </div>
                `;
                testDiv.className = 'test-case error';
            }
        }

        async function runAllTests() {
            clearResults();
            
            const tests = [
                {
                    name: '测试1: 标准音质 + down类型',
                    url: 'https://music.163.com/song?id=1901371647',
                    quality: 'standard'
                },
                {
                    name: '测试2: 无损音质 + down类型',
                    url: 'https://music.163.com/song?id=1901371647',
                    quality: 'lossless'
                },
                {
                    name: '测试3: 歌曲ID + down类型',
                    url: '1901371647',
                    quality: 'standard'
                }
            ];

            for (const test of tests) {
                await testDownType(test.name, test.url, test.quality);
                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
